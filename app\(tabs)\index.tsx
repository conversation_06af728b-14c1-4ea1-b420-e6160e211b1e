import React, { useState, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, Image, Modal, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { Camera, Image as ImageIcon, RotateCcw, CircleCheck as CheckCircle, Info, X } from 'lucide-react-native';
import { ValidationProgress } from '@/components/ValidationProgress';
import { ValidationResults } from '@/components/ValidationResults';
import { ValidationDescription } from '@/components/ValidationDescription';

type ValidationStage = 'idle' | 'stage1' | 'stage2' | 'stage3' | 'complete';

export default function ValidatorScreen() {
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [showCamera, setShowCamera] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [validationStage, setValidationStage] = useState<ValidationStage>('idle');
  const [validationResults, setValidationResults] = useState<any>(null);
  const [showLearnMore, setShowLearnMore] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const cameraRef = useRef<CameraView>(null);

  if (!permission) {
    return <View style={styles.container} />;
  }

  if (!permission.granted) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.permissionContainer}>
          <Camera size={64} color="#6366F1" />
          <Text style={styles.permissionTitle}>Camera Access Required</Text>
          <Text style={styles.permissionMessage}>
            We need camera access to capture and validate images
          </Text>
          <TouchableOpacity style={styles.primaryButton} onPress={requestPermission}>
            <Text style={styles.primaryButtonText}>Grant Permission</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const takePicture = async () => {
    if (cameraRef.current) {
      try {
        const photo = await cameraRef.current.takePictureAsync({
          quality: 1,
          base64: false,
        });
        setSelectedImage(photo.uri);
        setShowCamera(false);
      } catch (error) {
        Alert.alert('Error', 'Failed to take picture');
      }
    }
  };

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 4],
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        setSelectedImage(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const startValidation = () => {
    if (!selectedImage) {
      Alert.alert('Error', 'Please select an image first');
      return;
    }

    setValidationStage('stage1');
    
    // Simulate validation process
    setTimeout(() => setValidationStage('stage2'), 2000);
    setTimeout(() => setValidationStage('stage3'), 4000);
    setTimeout(() => {
      setValidationStage('complete');
      const tier1Score = Math.floor(Math.random() * 40) + 60;
      const tier2Score = Math.floor(Math.random() * 40) + 60;
      const tier3Score = Math.floor(Math.random() * 40) + 60;
      const isAiGenerated = Math.random() > 0.5;

      setValidationResults({
        isAiGenerated,
        confidence: Math.floor(Math.random() * 30) + 70,
        tier1: {
          passed: tier1Score >= 70,
          score: tier1Score
        },
        tier2: {
          passed: tier2Score >= 70,
          score: tier2Score
        },
        tier3: {
          passed: tier3Score >= 70,
          score: tier3Score
        },
      });
    }, 6000);
  };

  const resetValidation = () => {
    setValidationStage('idle');
    setValidationResults(null);
    setSelectedImage(null);
  };

  if (showCamera) {
    return (
      <SafeAreaView style={styles.fullScreen}>
        <CameraView style={styles.camera} facing={facing} ref={cameraRef}>
          <View style={styles.cameraOverlay}>
            <View style={styles.cameraHeader}>
              <TouchableOpacity
                style={styles.cameraButton}
                onPress={() => setShowCamera(false)}>
                <Text style={styles.cameraButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.cameraButton} onPress={toggleCameraFacing}>
                <RotateCcw size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
            <View style={styles.cameraFooter}>
              <TouchableOpacity style={styles.captureButton} onPress={takePicture}>
                <View style={styles.captureButtonInner} />
              </TouchableOpacity>
            </View>
          </View>
        </CameraView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.headerIcon}>
            <Text style={styles.headerIconText}>🤖</Text>
          </View>
          <View style={styles.headerTextContainer}>
            <Text style={styles.headerTitle}>AI Image Validator Pro</Text>
            <Text style={styles.headerSubtitle}>Advanced AI Detection System</Text>
          </View>
        </View>
      </View>

      {validationStage === 'idle' && (
        <View style={styles.introSection}>
          <Text style={styles.introText}>
            Our multi-stage pipeline validates AI-generated images with 92% accuracy, helping you distinguish them from real photos. We keep improving our methods for better results.
          </Text>
        </View>
      )}

      {validationStage !== 'idle' ? (
        // Show full-screen validation progress when processing
        <ValidationProgress
          stage={validationStage}
          selectedImage={selectedImage}
          onCancel={resetValidation}
          onViewResults={() => {
            console.log('View Results pressed, validationResults:', validationResults);
            console.log('Setting showResults to true');
            setShowResults(true);
            console.log('showResults state after setting:', true);
          }}
        />
      ) : (
        // Show normal content when idle
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.content}>
            {selectedImage ? (
              <View style={styles.imagePreview}>
                <Image source={{ uri: selectedImage }} style={styles.previewImage} />
                <View style={styles.imageActions}>
                  <TouchableOpacity style={styles.secondaryButton} onPress={resetValidation}>
                    <Text style={styles.secondaryButtonText}>New Image</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.primaryButton} onPress={startValidation}>
                    <Text style={styles.primaryButtonText}>Start Validation</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <View style={styles.imageSelector}>
                <View style={styles.placeholderImage}>
                  <ImageIcon size={64} color="#9CA3AF" />
                  <Text style={styles.placeholderText}>Select an image to validate</Text>
                </View>
                <View style={styles.selectorActions}>
                  <TouchableOpacity style={styles.actionButton} onPress={() => setShowCamera(true)}>
                    <Camera size={24} color="#FFFFFF" />
                    <Text style={styles.actionButtonText}>Camera</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.actionButton} onPress={pickImage}>
                    <ImageIcon size={24} color="#FFFFFF" />
                    <Text style={styles.actionButtonText}>Gallery</Text>
                  </TouchableOpacity>
                </View>

                <TouchableOpacity style={styles.learnMoreButton} onPress={() => setShowLearnMore(true)}>
                  <Info size={20} color="#007AFF" />
                  <Text style={styles.learnMoreText}>Learn More About Our Process</Text>
                </TouchableOpacity>
              </View>
            )}

            {validationStage === 'complete' && validationResults && (
              <ValidationResults results={validationResults} />
            )}
          </View>
        </ScrollView>
      )}

      {/* Learn More Modal */}
      <Modal
        visible={showLearnMore}
        animationType="slide"
        transparent={false}
        onRequestClose={() => setShowLearnMore(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>How We Work</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowLearnMore(false)}
            >
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            <ValidationDescription />
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Results Modal - Using conditional rendering for better Android compatibility */}
      {showResults && (
        <View style={styles.fullScreenModal}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Validation Results</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowResults(false)}
              >
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
              {validationResults ? (
                <View style={styles.resultsContainer}>
                  {/* Overall Result */}
                  <View style={styles.overallResult}>
                    <View style={styles.resultIcon}>
                      <Text style={styles.resultIconText}>
                        {validationResults.isAiGenerated ? '🤖' : '📸'}
                      </Text>
                    </View>
                    <Text style={styles.overallResultTitle}>
                      {validationResults.isAiGenerated ? 'AI Generated Image' : 'Authentic Image'}
                    </Text>
                    <Text style={styles.overallResultSubtitle}>
                      Confidence: {validationResults.confidence}%
                    </Text>
                  </View>

                  {/* Stage Results */}
                  <View style={styles.stageResults}>
                    <Text style={styles.stageResultsTitle}>Stage Analysis</Text>

                    <View style={styles.stageResultItem}>
                      <View style={styles.stageResultHeader}>
                        <Text style={styles.stageResultName}>Stage 1: Metadata Screening</Text>
                        <View style={[styles.stageResultBadge, validationResults.tier1.passed && styles.stageResultBadgeSuccess]}>
                          <Text style={[styles.stageResultBadgeText, validationResults.tier1.passed && styles.stageResultBadgeTextSuccess]}>
                            {validationResults.tier1.passed ? 'PASS' : 'FAIL'}
                          </Text>
                        </View>
                      </View>
                      <Text style={styles.stageResultDescription}>
                        Checks for authentic camera metadata and EXIF data
                      </Text>
                      <Text style={styles.stageResultScore}>Score: {validationResults.tier1.score}/100</Text>
                    </View>

                    <View style={styles.stageResultItem}>
                      <View style={styles.stageResultHeader}>
                        <Text style={styles.stageResultName}>Stage 2: Error Level Analysis</Text>
                        <View style={[styles.stageResultBadge, validationResults.tier2.passed && styles.stageResultBadgeSuccess]}>
                          <Text style={[styles.stageResultBadgeText, validationResults.tier2.passed && styles.stageResultBadgeTextSuccess]}>
                            {validationResults.tier2.passed ? 'PASS' : 'FAIL'}
                          </Text>
                        </View>
                      </View>
                      <Text style={styles.stageResultDescription}>
                        Analyzes compression patterns and pixel inconsistencies
                      </Text>
                      <Text style={styles.stageResultScore}>Score: {validationResults.tier2.score}/100</Text>
                    </View>

                    <View style={styles.stageResultItem}>
                      <View style={styles.stageResultHeader}>
                        <Text style={styles.stageResultName}>Stage 3: AI Detection (SINet)</Text>
                        <View style={[styles.stageResultBadge, validationResults.tier3.passed && styles.stageResultBadgeSuccess]}>
                          <Text style={[styles.stageResultBadgeText, validationResults.tier3.passed && styles.stageResultBadgeTextSuccess]}>
                            {validationResults.tier3.passed ? 'PASS' : 'FAIL'}
                          </Text>
                        </View>
                      </View>
                      <Text style={styles.stageResultDescription}>
                        Deep learning model for final AI detection
                      </Text>
                      <Text style={styles.stageResultScore}>Score: {validationResults.tier3.score}/100</Text>
                    </View>
                  </View>

                  {/* Test Another Image Button */}
                  <TouchableOpacity
                    style={styles.testAnotherButton}
                    onPress={() => {
                      setShowResults(false);
                      resetValidation();
                    }}
                  >
                    <Text style={styles.testAnotherButtonText}>Test Another Image</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={styles.resultsContainer}>
                  <Text style={styles.overallResultTitle}>Loading Results...</Text>
                  <Text style={styles.overallResultSubtitle}>Please wait while we prepare your validation results.</Text>
                </View>
              )}
            </ScrollView>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7', // iOS system background
  },
  fullScreen: {
    flex: 1,
    backgroundColor: '#000000',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionTitle: {
    fontSize: 22,
    fontWeight: '600', // iOS semibold
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
    fontFamily: 'System', // iOS system font
  },
  permissionMessage: {
    fontSize: 17,
    color: '#3C3C43', // iOS secondary label
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
    fontFamily: 'System',
  },
  header: {
    paddingTop: 20,
    paddingBottom: 24,
    paddingHorizontal: 20,
    backgroundColor: '#FFFFFF',
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  headerIcon: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  headerIconText: {
    fontSize: 28,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700', // iOS bold
    color: '#000000',
    marginBottom: 2,
    fontFamily: 'System',
    letterSpacing: 0.2,
  },
  headerSubtitle: {
    fontSize: 15,
    color: '#007AFF', // iOS system blue
    fontWeight: '500', // iOS medium
    fontFamily: 'System',
    letterSpacing: 0.1,
  },
  introSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
    borderRadius: 12, // iOS card radius
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 0.5,
    borderColor: '#C6C6C8', // iOS separator
  },
  introText: {
    fontSize: 15,
    color: '#3C3C43', // iOS secondary label
    lineHeight: 20,
    textAlign: 'center',
    fontWeight: '400',
    fontFamily: 'System',
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20, // Extra padding at bottom for scroll
  },
  content: {
    padding: 16, // iOS standard spacing
    minHeight: '100%', // Ensure content fills available space
  },
  imageSelector: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12, // iOS card radius
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 0.5,
    borderColor: '#C6C6C8', // iOS separator
  },
  placeholderImage: {
    alignItems: 'center',
    marginBottom: 24,
  },
  placeholderText: {
    fontSize: 17,
    color: '#3C3C43', // iOS secondary label
    fontWeight: '500', // iOS medium
    marginTop: 12,
    fontFamily: 'System',
  },
  selectorActions: {
    flexDirection: 'row',
    gap: 12, // iOS spacing
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#007AFF', // iOS system blue
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 10, // iOS button radius
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
    gap: 6,
    minWidth: 120,
    justifyContent: 'center',
  },
  actionButtonText: {
    fontSize: 17,
    fontWeight: '600', // iOS semibold
    color: '#FFFFFF',
    fontFamily: 'System',
  },
  learnMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
    gap: 6,
  },
  learnMoreText: {
    fontSize: 15,
    fontWeight: '400', // iOS regular
    color: '#007AFF', // iOS system blue
    fontFamily: 'System',
  },
  imagePreview: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12, // iOS card radius
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 0.5,
    borderColor: '#C6C6C8',
  },
  previewImage: {
    width: '100%',
    height: 280,
    resizeMode: 'cover',
  },
  imageActions: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    justifyContent: 'space-between',
  },
  primaryButton: {
    flex: 1,
    backgroundColor: '#007AFF', // iOS system blue
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 10, // iOS button radius
    alignItems: 'center',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600', // iOS semibold
    fontFamily: 'System',
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: '#F2F2F7', // iOS secondary system background
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 10,
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: '#C6C6C8',
  },
  secondaryButtonText: {
    color: '#3C3C43', // iOS label
    fontSize: 17,
    fontWeight: '400', // iOS regular
    fontFamily: 'System',
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'space-between',
  },
  cameraHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    paddingTop: 44, // iOS safe area
  },
  cameraButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 10, // iOS button radius
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  cameraButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600', // iOS semibold
    fontFamily: 'System',
  },
  cameraFooter: {
    alignItems: 'center',
    paddingBottom: 44, // iOS safe area
  },
  captureButton: {
    width: 76,
    height: 76,
    borderRadius: 38,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#F2F2F7',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#007AFF', // iOS system blue
  },
  fullScreenModal: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    backgroundColor: '#F2F2F7',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#F2F2F7', // iOS system background
    paddingTop: 44, // Status bar padding for Android/iOS
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 0.5, // iOS hairline
    borderBottomColor: '#C6C6C8', // iOS separator
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 0.5 },
    shadowOpacity: 0.1,
    shadowRadius: 0,
    elevation: 1,
  },
  modalTitle: {
    fontSize: 17,
    fontWeight: '600', // iOS semibold
    color: '#000000',
    fontFamily: 'System',
  },
  closeButton: {
    padding: 8,
    borderRadius: 20, // iOS circular button
    backgroundColor: '#F2F2F7', // iOS secondary system background
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    flex: 1,
    padding: 16, // iOS standard spacing
  },
  resultsContainer: {
    gap: 24,
  },
  overallResult: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 0.5,
    borderColor: '#C6C6C8',
  },
  resultIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  resultIconText: {
    fontSize: 32,
  },
  overallResultTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: 'System',
  },
  overallResultSubtitle: {
    fontSize: 17,
    color: '#3C3C43',
    textAlign: 'center',
    fontFamily: 'System',
  },
  stageResults: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 0.5,
    borderColor: '#C6C6C8',
  },
  stageResultsTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
    fontFamily: 'System',
  },
  stageResultItem: {
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 0.5,
    borderBottomColor: '#C6C6C8',
  },
  stageResultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  stageResultName: {
    fontSize: 17,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
    fontFamily: 'System',
  },
  stageResultBadge: {
    backgroundColor: '#FF3B30',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  stageResultBadgeSuccess: {
    backgroundColor: '#34C759',
  },
  stageResultBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'System',
  },
  stageResultBadgeTextSuccess: {
    color: '#FFFFFF',
  },
  stageResultDescription: {
    fontSize: 15,
    color: '#3C3C43',
    marginBottom: 8,
    fontFamily: 'System',
  },
  stageResultScore: {
    fontSize: 15,
    fontWeight: '500',
    color: '#007AFF',
    fontFamily: 'System',
  },
  testAnotherButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  testAnotherButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '600',
    fontFamily: 'System',
  },
});