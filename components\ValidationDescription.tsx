import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export function ValidationDescription() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>How We Work</Text>
      <Text style={styles.description}>
        We take pride in delivering accurate AI-image validation through our meticulously designed multi-stage pipeline. Our goal is crystal clear: ensure you can reliably distinguish AI-generated images from genuine captures.
      </Text>
      
      <Text style={styles.sectionTitle}>🛠️ Brief Overview of Our Validation Pipeline:</Text>
      
      <View style={styles.stageContainer}>
        <Text style={styles.stageTitle}>Metadata Screening:</Text>
        <Text style={styles.stageDescription}>Quickly rejects images missing real metadata</Text>
      </View>
      
      <View style={styles.stageContainer}>
        <Text style={styles.stageTitle}>Error Level Analysis (ELA):</Text>
        <Text style={styles.stageDescription}>Uses PIL and OpenCV to identify compression inconsistencies typical in AI-generated images.</Text>
      </View>
      
      <View style={styles.stageContainer}>
        <Text style={styles.stageTitle}>Deep Learning AI Detection (SINet):</Text>
        <Text style={styles.stageDescription}>Employs a powerful AI model to conclusively classify images as AI-generated or genuine.</Text>
      </View>
      
      <View style={styles.successRate}>
        <Text style={styles.successTitle}>🎯 Our Success Rate:</Text>
        <Text style={styles.successDescription}>
          Approximately 92% accuracy in validation. While highly effective, we acknowledge occasional challenges and continually improve our methods.
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12, // iOS card radius
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 0.5,
    borderColor: '#C6C6C8', // iOS separator
  },
  title: {
    fontSize: 22,
    fontWeight: '600', // iOS semibold
    color: '#000000',
    marginBottom: 12,
    textAlign: 'center',
    fontFamily: 'System',
  },
  description: {
    fontSize: 15,
    color: '#3C3C43', // iOS secondary label
    lineHeight: 20,
    marginBottom: 16,
    textAlign: 'center',
    fontFamily: 'System',
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '600', // iOS semibold
    color: '#000000',
    marginBottom: 12,
    fontFamily: 'System',
  },
  stageContainer: {
    marginBottom: 12,
    paddingLeft: 4,
  },
  stageTitle: {
    fontSize: 15,
    fontWeight: '600', // iOS semibold
    color: '#000000',
    marginBottom: 4,
    fontFamily: 'System',
  },
  stageDescription: {
    fontSize: 15,
    color: '#3C3C43', // iOS secondary label
    lineHeight: 20,
    paddingLeft: 8,
    fontFamily: 'System',
  },
  successRate: {
    marginTop: 12,
    padding: 16,
    backgroundColor: '#F2F9F2', // iOS-style light green
    borderRadius: 10, // iOS radius
    borderWidth: 0.5,
    borderColor: '#34C759', // iOS system green
  },
  successTitle: {
    fontSize: 15,
    fontWeight: '600', // iOS semibold
    color: '#1D4F2F', // iOS-style dark green
    marginBottom: 6,
    fontFamily: 'System',
  },
  successDescription: {
    fontSize: 15,
    color: '#1D4F2F', // iOS-style dark green
    lineHeight: 20,
    fontFamily: 'System',
  },
});
